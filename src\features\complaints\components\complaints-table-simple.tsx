'use client';

import { format } from 'date-fns';
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  Edit,
  Eye,
  FileText,
  MapPin,
  MoreHorizontal,
  Search,
} from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import {
  ComplaintWithRelations,
  useComplaints,
} from '../hooks/use-complaints-simple';
import { ComplaintTableProps, ComplaintUI } from '../types/ui-types';

const STATUS_CONFIG = {
  open: {
    label: 'Open',
    variant: 'outline' as const,
    icon: AlertCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
  },
  on_hold: {
    label: 'On Hold',
    variant: 'secondary' as const,
    icon: Clock,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
  },
  closed: {
    label: 'Closed',
    variant: 'outline' as const,
    icon: CheckCircle,
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
  },
} as const;

export function ComplaintsTable({
  onViewComplaint,
  onEditComplaint,
}: ComplaintTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Fetch real complaints data
  const { data: complaintsData = [], isLoading, error } = useComplaints();  // Convert database complaints to UI format
  const mapDbStatusToUI = (dbStatus: string, index: number): ComplaintUI['status'] => {
  // Map database status to UI status (database only has 'open', 'on_hold', 'closed')
  switch (dbStatus) {
    case 'open':
      return 'open';
    case 'on_hold':
      return 'on_hold';
    case 'closed':
      return 'closed';
    default:
      return 'open';
  }
  };
  // Helper function to determine if Section A is completed
  const isSectionACompleted = (complaint: ComplaintWithRelations): boolean => {
    return !!(
      complaint.email &&
      complaint.date &&
      complaint.expected_completion_date &&
      complaint.contractor_name &&
      complaint.location &&
      complaint.no_pma_lif &&
      complaint.description
    );
  };

  // Helper function to determine if Section B is completed
  const isSectionBCompleted = (complaint: ComplaintWithRelations): boolean => {
    return !!(
      complaint.actual_completion_date &&
      complaint.repair_completion_time &&
      complaint.cause_of_damage &&
      complaint.correction_action &&
      complaint.proof_of_repair_urls &&
      complaint.proof_of_repair_urls.length > 0
    );
  };  const complaints: ComplaintUI[] = complaintsData.map((complaint, index) => {
    const sectionAComplete = isSectionACompleted(complaint);
    const sectionBComplete = isSectionBCompleted(complaint);

    return {
      id: complaint.id,
      number:
        complaint.number ||
        `DCL-${new Date(complaint.created_at || '').getFullYear()}-${complaint.id.slice(-4)}`,
      email: complaint.email,
      date:
        complaint.date || complaint.created_at || new Date().toISOString(),
      expected_completion_date:
        complaint.expected_completion_date ||
        complaint.created_at ||
        new Date().toISOString(),
      actual_completion_date: complaint.actual_completion_date || null,
      contractor_name: complaint.contractor_name || 'N/A',
      location: complaint.location || null,
      involves_mantrap: complaint.involves_mantrap || false,
      no_pma_lif: complaint.no_pma_lif || '',
      description: complaint.description || '',
      status: mapDbStatusToUI(complaint.status, index),
      created_at: complaint.created_at || new Date().toISOString(),
      sectionACompleted: sectionAComplete,
      sectionBCompleted: sectionBComplete,
      repair_cost: sectionBComplete ? complaint.repair_cost || 0 : 0, // Show 0 until Section B completed
      cause_of_damage: complaint.cause_of_damage || null,
      correction_action: complaint.correction_action || null,
      repair_completion_time: complaint.repair_completion_time || null,
      proof_of_repair_urls: complaint.proof_of_repair_urls || [],
    };
  });  // Filter complaints based on search and status
  const filteredComplaints = complaints.filter((complaint) => {
    const matchesSearch =
      !searchTerm ||
      complaint.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      complaint.contractor_name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      complaint.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      complaint.no_pma_lif?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      complaint.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === 'all' || complaint.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Format date helper
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy');
    } catch {
      return dateString;
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Damage Complaint Logs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
              Loading complaints...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show error state
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Damage Complaint Logs
          </CardTitle>
        </CardHeader>{' '}
        <CardContent>
          <div className="flex flex-col items-center gap-2 py-8">
            <AlertCircle className="h-12 w-12 text-red-500" />
            <p className="text-red-600">Failed to load complaints</p>{' '}
            <p className="text-sm text-muted-foreground">
              {error instanceof Error
                ? error.message
                : 'Unknown error occurred'}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }
  // Render follow-up status
  const renderFollowUpStatus = (complaint: ComplaintUI) => {
    if (complaint.sectionACompleted && complaint.sectionBCompleted) {
      return (
        <div className="flex items-center gap-1 text-green-600">
          <CheckCircle className="h-4 w-4" />
          <span className="text-sm font-medium">Complete</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-1 text-blue-600">
          <AlertCircle className="h-4 w-4" />
          <span className="text-sm font-medium">Upload</span>
        </div>
      );
    }
  };
  // Render status badge
  const renderStatus = (status: ComplaintUI['status']) => {
    const config = STATUS_CONFIG[status];
    const Icon = config.icon;

    return (
      <Badge 
        variant="outline" 
        className={`flex items-center gap-1 ${config.bgColor} ${config.borderColor} ${config.color} border`}
      >
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Damage Complaint Logs
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by email, company, location, PMA LIF, or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9"
            />
          </div>{' '}          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="open">Open</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
              <SelectItem value="pending_approval">Pending Approval</SelectItem>
              <SelectItem value="verified">Verified</SelectItem>
              <SelectItem value="closed">Closed</SelectItem>
            </SelectContent>
          </Select>
        </div>{' '}
        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Complaint Date</TableHead>
                <TableHead>Expected Completion</TableHead>
                <TableHead>Actual Completion</TableHead>
                <TableHead>Company/Contractor</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>NO PMA LIF</TableHead>
                <TableHead>Cost (RM)</TableHead>
                <TableHead>Follow Up</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-16">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredComplaints.length > 0 ? (
                filteredComplaints.map((complaint) => (
                  <TableRow
                    key={complaint.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => onViewComplaint(complaint)}
                  >
                    <TableCell className="font-medium">
                      {complaint.email}
                    </TableCell>                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        {formatDate(complaint.date)}
                      </div>
                    </TableCell>{' '}                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        {formatDate(complaint.expected_completion_date)}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      {complaint.actual_completion_date ? (
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          {formatDate(complaint.actual_completion_date)}
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-sm">Not completed</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div
                        className="truncate max-w-48"
                        title={complaint.contractor_name || 'N/A'}
                      >
                        {complaint.contractor_name || 'N/A'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span
                          className="truncate max-w-32"
                          title={complaint.location || 'N/A'}
                        >
                          {complaint.location || 'N/A'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <code className="text-xs bg-muted px-1 py-0.5 rounded">
                        {complaint.no_pma_lif || 'N/A'}
                      </code>
                    </TableCell>                    <TableCell>
                      <div className="flex items-center gap-1">
                        <span className="text-sm font-medium">
                          {(complaint.repair_cost || 0).toFixed(2)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{renderFollowUpStatus(complaint)}</TableCell>
                    <TableCell>{renderStatus(complaint.status)}</TableCell>
                    <TableCell onClick={(e) => e.stopPropagation()}>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>{' '}
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => onViewComplaint(complaint)}
                            className="flex items-center gap-2"
                          >
                            <Eye className="h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          {onEditComplaint && (
                            <DropdownMenuItem
                              onClick={() => onEditComplaint(complaint)}
                              className="flex items-center gap-2"
                            >
                              <Edit className="h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={11} className="text-center py-8">
                    <div className="flex flex-col items-center gap-2">
                      <FileText className="h-12 w-12 text-muted-foreground" />
                      <p className="text-muted-foreground">
                        {searchTerm || statusFilter !== 'all'
                          ? 'No complaints found matching your criteria'
                          : 'No damage complaint logs found'}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {searchTerm || statusFilter !== 'all'
                          ? 'Try adjusting your search or filters'
                          : 'Create your first complaint log to get started'}
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        {/* Stats */}
        <div className="flex items-center justify-between mt-4">
          <p className="text-sm text-muted-foreground">
            Showing {filteredComplaints.length} of {complaints.length}{' '}
            complaints
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
